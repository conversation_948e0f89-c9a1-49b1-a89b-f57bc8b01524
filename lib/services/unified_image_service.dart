import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'encrypted_r2_service.dart';
import 'secure_image_upload_service.dart';
import 'image_cache_manager.dart';
import '../models/upload_result.dart';

/// 统一的图片服务
/// 
/// 功能：
/// 1. 统一的图片上传接口（头像、钓点照片等）
/// 2. 统一的签名URL生成
/// 3. 统一的图片显示组件
/// 4. 缓存管理
class UnifiedImageService {
  static final UnifiedImageService _instance = UnifiedImageService._internal();
  factory UnifiedImageService() => _instance;
  UnifiedImageService._internal();

  final SecureImageUploadService _uploadService = SecureImageUploadService();
  final EncryptedR2Service _r2Service = EncryptedR2Service();
  
  // 签名URL缓存
  final Map<String, String> _signedUrlCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const int _cacheExpiryMinutes = 50; // 缓存50分钟，签名URL有效期1小时

  /// 上传头像
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// 返回上传结果
  Future<ImageUploadResult?> uploadAvatar({
    required File imageFile,
    required String userId,
  }) async {
    debugPrint('🖼️ [统一图片服务] 开始上传头像');
    return await _uploadService.uploadImageIndependent(
      imageFile: imageFile,
      userId: userId,
    );
  }

  /// 上传钓点照片
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果
  Future<ImageUploadResult?> uploadSpotPhoto({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    debugPrint('🖼️ [统一图片服务] 开始上传钓点照片');
    return await _uploadService.uploadImageSecure(
      imageFile: imageFile,
      userId: userId,
      spotId: spotId,
    );
  }

  /// 获取签名URL
  /// [originalUrl] 原始R2 URL
  /// 返回签名URL，失败时返回原始URL
  Future<String> getSignedUrl(String originalUrl) async {
    try {
      // 检查缓存是否有效
      if (_signedUrlCache.containsKey(originalUrl) && 
          _cacheTimestamps.containsKey(originalUrl)) {
        final cacheTime = _cacheTimestamps[originalUrl]!;
        final now = DateTime.now();
        if (now.difference(cacheTime).inMinutes < _cacheExpiryMinutes) {
          debugPrint('🖼️ [统一图片服务] 使用缓存的签名URL');
          return _signedUrlCache[originalUrl]!;
        } else {
          // 缓存过期，清除
          _signedUrlCache.remove(originalUrl);
          _cacheTimestamps.remove(originalUrl);
        }
      }

      // 从原始URL中提取对象键
      final objectKey = _extractObjectKey(originalUrl);
      if (objectKey.isEmpty) {
        debugPrint('❌ [统一图片服务] 无法从URL中提取对象键: $originalUrl');
        return originalUrl; // 返回原始URL作为后备
      }

      debugPrint('🔍 [统一图片服务] 提取的对象键: $objectKey');

      // 生成签名URL
      final signedUrl = await _r2Service.generatePresignedUrl(
        objectKey: objectKey,
        method: 'GET',
        expiresIn: 3600, // 1小时有效期
      );

      if (signedUrl != null) {
        // 缓存签名URL
        _signedUrlCache[originalUrl] = signedUrl;
        _cacheTimestamps[originalUrl] = DateTime.now();
        debugPrint('✅ [统一图片服务] 签名URL生成成功');
        return signedUrl;
      } else {
        debugPrint('❌ [统一图片服务] 签名URL生成失败，使用原始URL');
        return originalUrl;
      }
    } catch (e) {
      debugPrint('❌ [统一图片服务] 签名URL生成异常: $e');
      return originalUrl; // 返回原始URL作为后备
    }
  }

  /// 从URL中提取对象键
  String _extractObjectKey(String originalUrl) {
    try {
      final uri = Uri.parse(originalUrl);
      final pathSegments = uri.pathSegments;

      debugPrint('🔍 [统一图片服务] 解析URL: $originalUrl');
      debugPrint('🔍 [统一图片服务] 路径段: $pathSegments');

      // 对于新的URL格式，需要去掉存储桶名称部分
      // 新格式: https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com/fishing_app/...
      // 我们需要提取 fishing_app 后面的部分作为对象键
      if (pathSegments.isNotEmpty) {
        // 查找存储桶名称的位置
        int bucketIndex = -1;
        for (int i = 0; i < pathSegments.length; i++) {
          if (pathSegments[i] == 'fishing_app' || pathSegments[i] == 'fishing-app') {
            bucketIndex = i;
            break;
          }
        }

        String objectKey;
        if (bucketIndex >= 0 && bucketIndex < pathSegments.length - 1) {
          // 找到存储桶，提取后面的部分
          objectKey = pathSegments.sublist(bucketIndex + 1).join('/');
          debugPrint('✅ [统一图片服务] 找到存储桶 ${pathSegments[bucketIndex]}，提取对象键: $objectKey');
        } else {
          // 没找到存储桶标识，使用完整路径
          objectKey = pathSegments.join('/');
          debugPrint('🔍 [统一图片服务] 未找到存储桶标识，使用完整路径: $objectKey');
        }

        return objectKey;
      }

      debugPrint('❌ [统一图片服务] URL中没有路径段');
      return '';
    } catch (e) {
      debugPrint('❌ [统一图片服务] URL解析失败: $e');
      return '';
    }
  }

  /// 构建签名图片组件（带缓存）
  /// [originalUrl] 原始R2 URL
  /// [fit] 图片适配方式
  /// [width] 宽度
  /// [height] 高度
  /// [placeholder] 加载占位符
  /// [errorWidget] 错误显示组件
  /// [isAvatar] 是否为头像（使用不同的缓存管理器）
  Widget buildCachedSignedImage({
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? placeholder,
    Widget? errorWidget,
    bool isAvatar = false,
  }) {
    return FutureBuilder<String>(
      future: getSignedUrl(originalUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? Container(
            width: width,
            height: height,
            color: Colors.grey.shade200,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return errorWidget ?? _buildErrorWidget(width, height);
        }

        final signedUrl = snapshot.data!;
        debugPrint('🖼️ [缓存图片] 加载图片: $signedUrl');
        
        return CachedNetworkImage(
          imageUrl: signedUrl,
          cacheManager: isAvatar ? ImageCacheManager.avatars : ImageCacheManager.spotPhotos,
          fit: fit,
          width: width,
          height: height,
          placeholder: (context, url) {
            return placeholder ?? Container(
              width: width,
              height: height,
              color: Colors.grey.shade200,
              child: const Center(child: CircularProgressIndicator()),
            );
          },
          errorWidget: (context, url, error) {
            debugPrint('❌ [缓存图片] 图片加载失败: $url - $error');
            return errorWidget ?? _buildErrorWidget(width, height);
          },
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      },
    );
  }

  /// 构建签名图片组件（兼容旧版本，无缓存）
  /// [originalUrl] 原始R2 URL
  /// [fit] 图片适配方式
  /// [width] 宽度
  /// [height] 高度
  /// [placeholder] 加载占位符
  /// [errorWidget] 错误显示组件
  Widget buildSignedImage({
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // 默认使用缓存版本
    return buildCachedSignedImage(
      originalUrl: originalUrl,
      fit: fit,
      width: width,
      height: height,
      placeholder: placeholder,
      errorWidget: errorWidget,
      isAvatar: false,
    );
  }

  /// 构建错误显示组件
  Widget _buildErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text('图片加载失败', style: TextStyle(color: Colors.grey.shade500)),
          ],
        ),
      ),
    );
  }

  /// 构建签名头像组件（带缓存）
  /// [originalUrl] 原始R2 URL
  /// [radius] 头像半径
  /// [backgroundColor] 背景色
  /// [placeholderIcon] 占位符图标
  Widget buildSignedAvatar({
    required String originalUrl,
    required double radius,
    Color? backgroundColor,
    IconData placeholderIcon = Icons.person,
  }) {
    return FutureBuilder<String>(
      future: getSignedUrl(originalUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            child: Icon(
              placeholderIcon,
              size: radius * 0.8,
              color: Colors.white,
            ),
          );
        }

        final signedUrl = snapshot.data!;
        debugPrint('🖼️ [缓存头像] 加载头像: $signedUrl');
        
        return CachedNetworkImage(
          imageUrl: signedUrl,
          cacheManager: ImageCacheManager.avatars,
          imageBuilder: (context, imageProvider) => CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            backgroundImage: imageProvider,
          ),
          placeholder: (context, url) => CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 0.3),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          errorWidget: (context, url, error) {
            debugPrint('❌ [缓存头像] 头像加载失败: $url - $error');
            return CircleAvatar(
              radius: radius,
              backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 0.3),
              child: Icon(
                placeholderIcon,
                size: radius * 0.8,
                color: Colors.white,
              ),
            );
          },
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      },
    );
  }

  /// 清除签名URL缓存
  void clearSignedUrlCache() {
    _signedUrlCache.clear();
    _cacheTimestamps.clear();
    debugPrint('🧹 [统一图片服务] 签名URL缓存已清除');
  }

  /// 清除过期的签名URL缓存
  void clearExpiredSignedUrlCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value).inMinutes >= _cacheExpiryMinutes) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _signedUrlCache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 [统一图片服务] 清除了 ${expiredKeys.length} 个过期签名URL缓存');
    }
  }

  /// 清除所有图片缓存（包括签名URL和文件缓存）
  Future<void> clearAllCache() async {
    // 清除签名URL缓存
    clearSignedUrlCache();
    
    // 清除图片文件缓存
    await ImageCacheManager.clearAllCache();
    
    debugPrint('🧹 [统一图片服务] 所有缓存已清除');
  }

  /// 清除钓点照片缓存
  Future<void> clearSpotPhotosCache() async {
    await ImageCacheManager.clearSpotPhotosCache();
    debugPrint('🧹 [统一图片服务] 钓点照片缓存已清除');
  }

  /// 清除头像缓存
  Future<void> clearAvatarsCache() async {
    await ImageCacheManager.clearAvatarsCache();
    debugPrint('🧹 [统一图片服务] 头像缓存已清除');
  }

  /// 获取缓存统计信息
  Future<Map<String, dynamic>> getCacheStats() async {
    final imageCacheStats = await ImageCacheManager.getCacheStats();
    
    return {
      'signedUrlCache': {
        'count': _signedUrlCache.length,
        'expiryMinutes': _cacheExpiryMinutes,
      },
      'imageCache': imageCacheStats,
    };
  }

  /// 预加载图片
  Future<void> preloadImage(String originalUrl, {bool isAvatar = false}) async {
    try {
      final signedUrl = await getSignedUrl(originalUrl);
      await ImageCacheManager.preloadImage(signedUrl, isAvatar: isAvatar);
      debugPrint('✅ [统一图片服务] 图片预加载完成: $originalUrl');
    } catch (e) {
      debugPrint('❌ [统一图片服务] 图片预加载失败: $originalUrl - $e');
    }
  }

  /// 检查图片是否已缓存
  Future<bool> isImageCached(String originalUrl, {bool isAvatar = false}) async {
    try {
      final signedUrl = await getSignedUrl(originalUrl);
      return await ImageCacheManager.isImageCached(signedUrl, isAvatar: isAvatar);
    } catch (e) {
      debugPrint('❌ [统一图片服务] 检查缓存状态失败: $originalUrl - $e');
      return false;
    }
  }

  /// 兼容旧版本的clearCache方法
  @Deprecated('使用 clearSignedUrlCache() 或 clearAllCache() 替代')
  void clearCache() {
    clearSignedUrlCache();
  }

  /// 兼容旧版本的clearExpiredCache方法
  @Deprecated('使用 clearExpiredSignedUrlCache() 替代')
  void clearExpiredCache() {
    clearExpiredSignedUrlCache();
  }
}
