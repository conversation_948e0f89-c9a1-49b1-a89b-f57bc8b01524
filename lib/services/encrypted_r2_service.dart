import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'service_locator.dart';
import '../config/app_config.dart';

/// 加密的 R2 凭据服务
class EncryptedR2Service {
  // 解密密钥（与 PocketBase 相同）
  static const String _encryptionKey = 'fishing-app-secret-key-2025';
  
  // 内存中缓存的凭据
  static R2Credentials? _cachedCredentials;
  static DateTime? _credentialsTimestamp;
  static const int _credentialsExpiryHours = 23; // 凭据缓存23小时，服务端24小时过期
  
  /// 获取当前缓存的凭据
  R2Credentials? get cachedCredentials => _cachedCredentials;
  
  /// 检查凭据是否有效
  bool get hasValidCredentials {
    if (_cachedCredentials == null || _credentialsTimestamp == null) {
      return false;
    }
    
    final now = DateTime.now();
    final age = now.difference(_credentialsTimestamp!);
    return age.inHours < _credentialsExpiryHours;
  }
  
  /// 初始化只读凭据（应用启动时调用）
  Future<bool> initializeReadOnlyCredentials() async {
    try {
      debugPrint('🔐 [R2凭据] 初始化只读凭据');
      
      final credentials = await _fetchCredentialsFromServer(requireAuth: false);
      if (credentials != null) {
        _cachedCredentials = credentials;
        _credentialsTimestamp = DateTime.now();
        debugPrint('✅ [R2凭据] 只读凭据初始化成功，权限级别: ${credentials.permissionLevel}');
        return true;
      } else {
        debugPrint('❌ [R2凭据] 只读凭据初始化失败');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [R2凭据] 只读凭据初始化异常: $e');
      return false;
    }
  }
  
  /// 升级到读写凭据（用户登录后调用）
  Future<bool> upgradeToReadWriteCredentials() async {
    try {
      debugPrint('🔐 [R2凭据] 升级到读写凭据');
      
      final credentials = await _fetchCredentialsFromServer(requireAuth: true);
      if (credentials != null) {
        _cachedCredentials = credentials;
        _credentialsTimestamp = DateTime.now();
        debugPrint('✅ [R2凭据] 读写凭据升级成功，权限级别: ${credentials.permissionLevel}');
        return true;
      } else {
        debugPrint('❌ [R2凭据] 读写凭据升级失败');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [R2凭据] 读写凭据升级异常: $e');
      return false;
    }
  }
  
  /// 清除缓存的凭据（用户登出时调用）
  void clearCredentials() {
    _cachedCredentials = null;
    _credentialsTimestamp = null;
    debugPrint('🧹 [R2凭据] 凭据缓存已清除');
  }
  
  /// 获取加密的 R2 凭据（兼容旧接口，优先使用缓存）
  Future<R2Credentials?> getR2Credentials() async {
    // 优先使用缓存的凭据
    if (hasValidCredentials) {
      debugPrint('🔐 [R2凭据] 使用缓存的凭据，权限级别: ${_cachedCredentials!.permissionLevel}');
      return _cachedCredentials;
    }
    
    // 缓存无效，重新获取
    debugPrint('🔐 [R2凭据] 缓存无效，重新获取凭据');
    return await _fetchCredentialsFromServer(requireAuth: Services.auth.isLoggedIn);
  }
  
  /// 从服务器获取凭据的内部方法
  Future<R2Credentials?> _fetchCredentialsFromServer({required bool requireAuth}) async {
    try {
      final user = Services.auth.currentUser;
      debugPrint('🔐 [加密R2] 开始获取加密凭据');
      debugPrint('🔐 [加密R2] 用户状态: ${user != null ? "已登录" : "未登录"}');
      
      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final url = '$baseUrl/api/get-encrypted-r2-credentials';
      
      final headers = <String, String>{
        'Content-Type': 'application/json',
      };
      
      // 如果用户已登录，添加认证头
      if (user != null) {
        final token = Services.auth.token;
        if (token.isNotEmpty) {
          headers['Authorization'] = 'Bearer $token';
        }
      }
      
      debugPrint('🔐 [加密R2] 请求URL: $url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );
      
      debugPrint('🔐 [加密R2] 响应状态码: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('🔐 [加密R2] 权限级别: ${data['permissionLevel']}');
        
        // 解密凭据
        final encryptedCreds = data['encryptedCredentials'];
        final decryptedAccessKeyId = _simpleDecrypt(
          encryptedCreds['accessKeyId'], 
          _encryptionKey
        );
        final decryptedSecretAccessKey = _simpleDecrypt(
          encryptedCreds['secretAccessKey'], 
          _encryptionKey
        );
        
        debugPrint('🔐 [加密R2] 解密成功');
        debugPrint('🔐 [加密R2] AccessKeyId: ${decryptedAccessKeyId.substring(0, 8)}...');
        
        return R2Credentials(
          accessKeyId: decryptedAccessKeyId,
          secretAccessKey: decryptedSecretAccessKey,
          endpoint: data['baseConfig']['endpoint'],
          bucketName: data['baseConfig']['bucketName'],
          region: data['baseConfig']['region'],
          permissions: List<String>.from(encryptedCreds['permissions']),
          permissionLevel: data['permissionLevel'],
        );
      } else {
        debugPrint('❌ [加密R2] 获取凭据失败: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ [加密R2] 获取凭据异常: $e');
      return null;
    }
  }
  
  /// 生成预签名 URL（优先使用缓存凭据）
  Future<String?> generatePresignedUrl({
    required String objectKey,
    required String method,
    int expiresIn = 3600,
  }) async {
    try {
      // 优先使用缓存的凭据
      R2Credentials? credentials;
      
      if (hasValidCredentials) {
        credentials = _cachedCredentials;
        debugPrint('🔍 [预签名URL] 使用缓存的凭据生成URL');
      } else {
        // 缓存无效，重新获取
        debugPrint('🔍 [预签名URL] 缓存无效，重新获取凭据');
        credentials = await _fetchCredentialsFromServer(requireAuth: Services.auth.isLoggedIn);
        
        if (credentials != null) {
          _cachedCredentials = credentials;
          _credentialsTimestamp = DateTime.now();
        }
      }
      
      if (credentials == null) {
        debugPrint('❌ [预签名URL] 无法获取R2凭据');
        return null;
      }
      
      debugPrint('🔍 [预签名URL] 开始生成预签名URL');
      debugPrint('🔍 [预签名URL] 对象键: $objectKey');
      debugPrint('🔍 [预签名URL] 方法: $method');
      debugPrint('🔍 [预签名URL] 权限级别: ${credentials.permissionLevel}');
      
      // 检查权限
      if (method.toUpperCase() == 'PUT' && !credentials.permissions.contains('write')) {
        debugPrint('❌ [预签名URL] 无写入权限');
        return null;
      }
      
      if (method.toUpperCase() == 'DELETE' && !credentials.permissions.contains('delete')) {
        debugPrint('❌ [预签名URL] 无删除权限');
        return null;
      }
      
      // 生成预签名URL（使用标准的AWS S3签名算法）
      final presignedUrl = await _generateAwsPresignedUrl(
        credentials: credentials,
        objectKey: objectKey,
        method: method,
        expiresIn: expiresIn,
      );
      
      debugPrint('✅ [预签名URL] 生成成功');
      return presignedUrl;
      
    } catch (e) {
      debugPrint('❌ [预签名URL] 生成异常: $e');
      return null;
    }
  }
  
  /// 生成 AWS S3 预签名 URL（使用 Flutter 的 crypto 包）
  Future<String> _generateAwsPresignedUrl({
    required R2Credentials credentials,
    required String objectKey,
    required String method,
    required int expiresIn,
  }) async {
    final now = DateTime.now().toUtc();
    final amzDate = _formatAmzDate(now);
    final dateStamp = amzDate.substring(0, 8);
    
    debugPrint('🔍 [AWS签名] 时间戳: $amzDate');
    debugPrint('🔍 [AWS签名] 日期戳: $dateStamp');
    
    // 构建查询参数
    final credentialScope = '$dateStamp/${credentials.region}/s3/aws4_request';
    final credential = '${credentials.accessKeyId}/$credentialScope';
    
    final queryParams = {
      'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
      'X-Amz-Credential': credential,
      'X-Amz-Date': amzDate,
      'X-Amz-Expires': expiresIn.toString(),
      'X-Amz-SignedHeaders': 'host',
    };
    
    // 构建规范查询字符串
    final canonicalQueryString = queryParams.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    // 构建规范请求
    final host = credentials.endpoint.replaceAll(RegExp(r'^https?://'), '');
    final canonicalUri = '/${credentials.bucketName}/$objectKey';
    final canonicalHeaders = 'host:$host\n';
    const signedHeaders = 'host';
    const payloadHash = 'UNSIGNED-PAYLOAD';
    
    final canonicalRequest = [
      method.toUpperCase(),
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash,
    ].join('\n');
    
    debugPrint('🔍 [AWS签名] 规范请求构建完成');
    
    // 构建待签名字符串
    final hashedCanonicalRequest = sha256.convert(utf8.encode(canonicalRequest)).toString();
    final stringToSign = [
      'AWS4-HMAC-SHA256',
      amzDate,
      credentialScope,
      hashedCanonicalRequest,
    ].join('\n');
    
    debugPrint('🔍 [AWS签名] 待签名字符串构建完成');
    debugPrint('🔍 [AWS签名] 规范请求哈希: $hashedCanonicalRequest');
    
    // 计算签名（使用标准的 HMAC-SHA256 链式计算）
    final signature = _calculateAwsSignature(
      stringToSign: stringToSign,
      secretAccessKey: credentials.secretAccessKey,
      dateStamp: dateStamp,
      region: credentials.region,
    );
    
    debugPrint('🔍 [AWS签名] 签名计算完成: $signature');
    
    // 构建最终URL
    final finalUrl = '${credentials.endpoint}/${credentials.bucketName}/$objectKey?$canonicalQueryString&X-Amz-Signature=$signature';
    
    return finalUrl;
  }
  
  /// 计算 AWS 签名（使用 Flutter 的 crypto 包）
  String _calculateAwsSignature({
    required String stringToSign,
    required String secretAccessKey,
    required String dateStamp,
    required String region,
  }) {
    debugPrint('🔍 [HMAC计算] 开始链式计算');
    
    // HMAC-SHA256 链式计算
    var key = utf8.encode('AWS4$secretAccessKey');
    
    // kDate = HMAC-SHA256(dateStamp, "AWS4" + secretAccessKey)
    var kDate = Hmac(sha256, key).convert(utf8.encode(dateStamp)).bytes;
    debugPrint('🔍 [HMAC计算] kDate: ${kDate.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
    
    // kRegion = HMAC-SHA256(region, kDate)
    var kRegion = Hmac(sha256, kDate).convert(utf8.encode(region)).bytes;
    debugPrint('🔍 [HMAC计算] kRegion: ${kRegion.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
    
    // kService = HMAC-SHA256("s3", kRegion)
    var kService = Hmac(sha256, kRegion).convert(utf8.encode('s3')).bytes;
    debugPrint('🔍 [HMAC计算] kService: ${kService.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
    
    // kSigning = HMAC-SHA256("aws4_request", kService)
    var kSigning = Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;
    debugPrint('🔍 [HMAC计算] kSigning: ${kSigning.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
    
    // signature = HMAC-SHA256(stringToSign, kSigning)
    var signature = Hmac(sha256, kSigning).convert(utf8.encode(stringToSign));
    debugPrint('🔍 [HMAC计算] 最终签名: ${signature.toString()}');
    
    return signature.toString();
  }
  
  /// 格式化 AMZ 日期
  String _formatAmzDate(DateTime date) {
    final year = date.year.toString().padLeft(4, '0');
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    final second = date.second.toString().padLeft(2, '0');
    
    return '$year$month${day}T$hour$minute${second}Z';
  }
  
  /// 简单解密函数（与 PocketBase 相同的实现）
  String _simpleDecrypt(String encryptedText, String key) {
    final decoded = base64Decode(encryptedText);
    final result = <int>[];
    
    for (int i = 0; i < decoded.length; i++) {
      result.add(decoded[i] ^ key.codeUnitAt(i % key.length));
    }
    
    return String.fromCharCodes(result);
  }
}

/// R2 凭据数据类
class R2Credentials {
  final String accessKeyId;
  final String secretAccessKey;
  final String endpoint;
  final String bucketName;
  final String region;
  final List<String> permissions;
  final String permissionLevel;
  
  R2Credentials({
    required this.accessKeyId,
    required this.secretAccessKey,
    required this.endpoint,
    required this.bucketName,
    required this.region,
    required this.permissions,
    required this.permissionLevel,
  });
}