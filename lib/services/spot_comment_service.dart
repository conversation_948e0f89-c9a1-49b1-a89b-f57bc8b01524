import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import '../models/spot_comment.dart';
import 'service_locator.dart';

/// 钓点评论服务
/// 
/// 功能：
/// 1. 发布评论
/// 2. 获取评论列表
/// 3. 删除评论（仅自己的评论）
class SpotCommentService {
  static final SpotCommentService _instance = SpotCommentService._internal();
  factory SpotCommentService() => _instance;
  SpotCommentService._internal();

  /// 发布评论
  Future<bool> addComment({
    required String spotId,
    required String content,
  }) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [评论服务] 用户未登录，无法发布评论');
        return false;
      }

      debugPrint('💬 [评论服务] 开始发布评论: $spotId');

      final pb = PocketBaseConfig.instance.client;
      
      await pb.collection('spot_comments').create(body: {
        'spot_id': spotId,
        'user_id': currentUser.id,
        'username': currentUser.username ?? currentUser.email ?? '匿名用户',
        'content': content,
        'created_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ [评论服务] 评论发布成功');
      return true;
    } catch (e) {
      debugPrint('❌ [评论服务] 发布评论失败: $e');
      return false;
    }
  }

  /// 获取钓点评论列表
  Future<List<SpotComment>> getSpotComments(String spotId) async {
    try {
      debugPrint('💬 [评论服务] 开始获取评论列表: $spotId');

      final pb = PocketBaseConfig.instance.client;
      
      final records = await pb
          .collection('spot_comments')
          .getFullList(
            filter: 'spot_id = "$spotId"',
            sort: '-created_at', // 按时间倒序
          );

      final comments = records
          .map((record) => SpotComment.fromJson(record.toJson()))
          .toList();

      debugPrint('✅ [评论服务] 获取到 ${comments.length} 条评论');
      return comments;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取评论列表失败: $e');
      return [];
    }
  }

  /// 删除评论（仅限自己的评论）
  Future<bool> deleteComment(String commentId) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [评论服务] 用户未登录，无法删除评论');
        return false;
      }

      debugPrint('💬 [评论服务] 开始删除评论: $commentId');

      final pb = PocketBaseConfig.instance.client;
      
      // 先检查评论是否属于当前用户
      final comment = await pb.collection('spot_comments').getOne(commentId);
      if (comment.data['user_id'] != currentUser.id) {
        debugPrint('❌ [评论服务] 无权删除他人评论');
        return false;
      }

      await pb.collection('spot_comments').delete(commentId);

      debugPrint('✅ [评论服务] 评论删除成功');
      return true;
    } catch (e) {
      debugPrint('❌ [评论服务] 删除评论失败: $e');
      return false;
    }
  }

  /// 获取评论统计
  Future<int> getCommentCount(String spotId) async {
    try {
      final pb = PocketBaseConfig.instance.client;
      
      final result = await pb
          .collection('spot_comments')
          .getList(
            page: 1,
            perPage: 1,
            filter: 'spot_id = "$spotId"',
          );

      return result.totalItems;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取评论数量失败: $e');
      return 0;
    }
  }
}