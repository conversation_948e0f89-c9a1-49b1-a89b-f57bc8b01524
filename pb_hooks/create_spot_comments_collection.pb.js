// 创建 spot_comments 集合
routerAdd("POST", "/api/admin/create-spot-comments-collection", (c) => {
  console.log('收到创建spot_comments集合的请求');
  
  try {
    // 检查集合是否已存在
    try {
      const existingCollection = $app.dao().findCollectionByNameOrId("spot_comments");
      return c.json(200, {
        success: true,
        message: "spot_comments集合已存在",
        collectionId: existingCollection.id
      });
    } catch (e) {
      // 集合不存在，继续创建
    }
    
    // 创建集合
    const collection = new Collection();
    collection.name = "spot_comments";
    collection.type = "base";
    collection.system = false;
    
    // 定义字段
    const schema = [
      {
        name: "spot_id",
        type: "relation",
        required: true,
        options: {
          collectionId: "", // 需要在创建后设置
          cascadeDelete: true,
          minSelect: 1,
          maxSelect: 1,
          displayFields: ["name"]
        }
      },
      {
        name: "user_id", 
        type: "relation",
        required: true,
        options: {
          collectionId: "", // 需要在创建后设置
          cascadeDelete: false,
          minSelect: 1,
          maxSelect: 1,
          displayFields: ["username", "email"]
        }
      },
      {
        name: "username",
        type: "text",
        required: true,
        options: {
          min: 1,
          max: 100,
          pattern: ""
        }
      },
      {
        name: "content",
        type: "text",
        required: true,
        options: {
          min: 1,
          max: 500,
          pattern: ""
        }
      },
      {
        name: "created_at",
        type: "date",
        required: true,
        options: {
          min: "",
          max: ""
        }
      }
    ];
    
    collection.schema = schema;
    
    // 设置API规则 - 允许查看公开钓点的评论，只能管理自己的评论
    collection.listRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    collection.viewRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    collection.createRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.updateRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.deleteRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    
    // 保存集合
    $app.dao().saveCollection(collection);
    
    // 更新关联字段的collectionId
    try {
      const fishingSpotsCollection = $app.dao().findCollectionByNameOrId("fishing_spots");
      const usersCollection = $app.dao().findCollectionByNameOrId("users");
      
      // 更新schema中的关联ID
      const updatedSchema = collection.schema.map(field => {
        if (field.name === "spot_id") {
          field.options.collectionId = fishingSpotsCollection.id;
        } else if (field.name === "user_id") {
          field.options.collectionId = usersCollection.id;
        }
        return field;
      });
      
      collection.schema = updatedSchema;
      $app.dao().saveCollection(collection);
      
    } catch (relationError) {
      console.log('关联集合设置警告:', relationError.message);
    }
    
    return c.json(200, {
      success: true,
      message: "spot_comments集合创建成功",
      collectionId: collection.id
    });
    
  } catch (error) {
    console.error('创建spot_comments集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('spot_comments集合创建脚本已加载');