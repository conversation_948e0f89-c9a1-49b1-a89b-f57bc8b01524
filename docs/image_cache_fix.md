# 图片缓存丢失问题修复方案

## 问题描述

用户反馈：当app在手机上退出后重新开启，之前缓存的图片就没有了，需要重新下载。

## 问题根本原因

通过代码分析发现，图片缓存丢失的主要原因是：

1. **缓存存储位置问题**：
   - 项目使用 `flutter_cache_manager` 进行图片缓存
   - 默认情况下，`flutter_cache_manager` 使用 `getTemporaryDirectory()` 作为缓存目录
   - 在 Android 系统中，临时目录可能会在以下情况被清理：
     - 系统内存不足时
     - 应用更新时
     - 系统清理缓存时
     - 应用长时间未使用时

2. **缓存配置不当**：
   - 当前的 `ImageCacheManager` 配置使用了默认的缓存位置
   - 没有明确指定持久化的存储目录

## 解决方案

### 1. 修改缓存存储位置

将图片缓存从临时目录迁移到应用文档目录：

- **原来**：使用 `getTemporaryDirectory()` （容易被系统清理）
- **现在**：使用 `getApplicationDocumentsDirectory()` （持久化存储）

### 2. 更新 ImageCacheManager 配置

修改 `lib/services/image_cache_manager.dart`：

```dart
/// 初始化缓存管理器
/// 创建持久化缓存目录
static Future<void> initialize() async {
  final appDocDir = await getApplicationDocumentsDirectory();
  final spotPhotosDir = Directory(path.join(appDocDir.path, 'image_cache', 'spot_photos'));
  final avatarsDir = Directory(path.join(appDocDir.path, 'image_cache', 'avatars'));
  
  // 确保目录存在
  if (!await spotPhotosDir.exists()) {
    await spotPhotosDir.create(recursive: true);
  }
  
  // 创建使用持久化目录的缓存管理器
  _spotPhotos = CacheManager(
    Config(
      _cacheKey,
      stalePeriod: _cacheExpiry,
      maxNrOfCacheObjects: _maxCacheObjects,
      repo: JsonCacheInfoRepository(databaseName: _cacheKey),
      fileService: HttpFileService(),
      fileSystem: IOFileSystem(spotPhotosDir.path), // 使用持久化目录
    ),
  );
}
```

### 3. 在应用启动时初始化

在 `lib/services/service_locator.dart` 中添加初始化调用：

```dart
// 初始化图片缓存管理器
await _initializeImageCacheManager();
```

### 4. 更新图片组件使用缓存

修改 `lib/widgets/spot_details_sheet.dart` 中的图片加载组件，使用统一图片服务的缓存功能：

```dart
/// 构建签名图片组件（使用缓存）
Widget _buildSignedImage({
  required String originalUrl,
  BoxFit fit = BoxFit.cover,
  double? width,
  double? height,
}) {
  // 使用统一图片服务的缓存组件
  return _imageService.buildCachedSignedImage(
    originalUrl: originalUrl,
    fit: fit,
    width: width,
    height: height,
    isAvatar: false, // 钓点照片不是头像
  );
}
```

## 修复效果

### 修复前
- 图片缓存存储在临时目录
- 应用重启后缓存可能丢失
- 用户需要重新下载图片

### 修复后
- 图片缓存存储在应用文档目录
- 应用重启后缓存保持不变
- 用户无需重新下载已缓存的图片

## 缓存目录结构

```
/data/data/com.example.fishing_app/app_flutter/documents/
└── image_cache/
    ├── spot_photos/          # 钓点照片缓存
    │   ├── cached_image_1
    │   ├── cached_image_2
    │   └── ...
    └── avatars/              # 头像缓存
        ├── cached_avatar_1
        ├── cached_avatar_2
        └── ...
```

## 测试验证

1. **功能测试**：
   - 启动应用，浏览钓点照片
   - 完全退出应用
   - 重新启动应用
   - 验证之前浏览的照片无需重新下载

2. **单元测试**：
   - 运行 `test/image_cache_test.dart`
   - 验证缓存管理器初始化正常
   - 验证缓存操作不会抛出异常

## 注意事项

1. **存储空间**：持久化缓存会占用更多存储空间，但提供了更好的用户体验
2. **缓存清理**：应用仍然支持手动清理缓存功能
3. **向后兼容**：如果初始化失败，会回退到默认配置，不影响应用正常运行

## 相关文件

- `lib/services/image_cache_manager.dart` - 缓存管理器配置
- `lib/services/service_locator.dart` - 服务初始化
- `lib/widgets/spot_details_sheet.dart` - 图片显示组件
- `test/image_cache_test.dart` - 单元测试
